/* Custom styles for Barbershop Scraper */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --light-bg: #f8f9fa;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-bg);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

main {
    flex: 1;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    border: none;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #34495e, #2980b9);
    transform: translateY(-1px);
}

.quick-location {
    transition: all 0.3s ease;
}

.quick-location:hover {
    transform: scale(1.05);
}

/* Progress bar */
.progress {
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    background: linear-gradient(90deg, var(--secondary-color), var(--success-color));
}

/* Feature icons */
.feature-icon i {
    transition: all 0.3s ease;
}

.feature-icon:hover i {
    transform: scale(1.1);
    color: var(--secondary-color) !important;
}

/* Barbershop cards */
.barbershop-card .card {
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.barbershop-card:hover .card {
    border-left-color: var(--primary-color);
}

.barbershop-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
}

/* Star ratings */
.text-warning i {
    color: #ffc107 !important;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.4em 0.6em;
    border-radius: 6px;
}

/* Form elements */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Alert styles */
.alert {
    border-radius: 10px;
    border: none;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

/* Footer */
footer {
    margin-top: auto;
    border-top: 1px solid #e9ecef;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .display-4 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
    }
}

/* Loading animations */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading {
    animation: pulse 2s infinite;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Status indicators */
.status-pending {
    color: #6c757d;
}

.status-running {
    color: var(--secondary-color);
}

.status-completed {
    color: var(--success-color);
}

.status-error {
    color: var(--accent-color);
}

/* Search filter highlight */
.search-highlight {
    background-color: yellow;
    padding: 1px 3px;
    border-radius: 3px;
}
