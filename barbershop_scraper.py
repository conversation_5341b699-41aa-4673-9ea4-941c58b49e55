import json
import time
import requests
from bs4 import BeautifulSoup
from openai import OpenAI
import logging
from typing import List, Dict, Optional
import re
from urllib.parse import urljoin, quote
import random
from config import DEEPSEEK_API_KEY, DEEPSEEK_BASE_URL, DEEPSEEK_CONFIG, SCRAPING_CONFIG, BARBERSHOP_SCHEMA

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BarbershopScraper:
    def __init__(self, deepseek_api_key: str = None):
        api_key = deepseek_api_key or DEEPSEEK_API_KEY
        self.client = OpenAI(
            api_key=api_key,
            base_url=DEEPSEEK_BASE_URL
        )
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
    def scrape_yelp_barbershops(self, city: str, state: str, limit: int = 20) -> List[str]:
        """Scrape barbershop listings from Yelp"""
        barbershops_data = []
        
        # Yelp search URL
        search_url = f"https://www.yelp.com/search?find_desc=barbershop&find_loc={quote(city)}%2C+{quote(state)}"
        
        try:
            response = self.session.get(search_url)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find business listings
            business_cards = soup.find_all('div', {'data-testid': 'serp-ia-card'}) or \
                           soup.find_all('div', class_=re.compile(r'businessName'))
            
            for card in business_cards[:limit]:
                raw_text = card.get_text(separator=' ', strip=True)
                if raw_text and len(raw_text) > 50:  # Filter out empty/short entries
                    barbershops_data.append(raw_text)
                    
            time.sleep(random.uniform(1, 3))  # Rate limiting
            
        except Exception as e:
            logger.error(f"Error scraping Yelp: {e}")
            
        return barbershops_data
    
    def scrape_google_maps_data(self, query: str, location: str) -> List[str]:
        """Scrape barbershop data from Google Maps search results"""
        barbershops_data = []
        
        # Google Maps search URL
        search_url = f"https://www.google.com/maps/search/{quote(query)}+{quote(location)}"
        
        try:
            response = self.session.get(search_url)
            response.raise_for_status()
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract business information from search results
            business_elements = soup.find_all('div', class_=re.compile(r'fontBodyMedium|fontBodySmall'))
            
            current_business = ""
            for element in business_elements:
                text = element.get_text(strip=True)
                if text and len(text) > 10:
                    current_business += f" {text}"
                    if len(current_business) > 200:  # Reasonable business description length
                        barbershops_data.append(current_business.strip())
                        current_business = ""
                        
            time.sleep(random.uniform(2, 4))  # Rate limiting
            
        except Exception as e:
            logger.error(f"Error scraping Google Maps: {e}")
            
        return barbershops_data
    
    def process_with_deepseek(self, raw_data: str, retry_count: int = 3) -> Optional[Dict]:
        """Process raw barbershop data using DeepSeek API with JSON output"""
        
        system_prompt = """You are a data extraction specialist. Extract barbershop information from the provided text and output it in JSON format.

Required fields: name, address, city, phone
Optional fields: state, zip, website, rating, review_count, latitude, longitude, description, services (array), hours, featured (boolean), image_url, logo_url, neighborhood, price_level, category

EXAMPLE JSON OUTPUT:
{
  "name": "Example Barbershop",
  "address": "123 Main St",
  "city": "San Diego", 
  "state": "CA",
  "zip": "92101",
  "phone": "(*************",
  "website": "http://www.examplebarbershop.com",
  "rating": 4.7,
  "review_count": 150,
  "latitude": 32.7157,
  "longitude": -117.1611,
  "description": "A brief description of the barbershop.",
  "services": ["Haircuts", "Beard Trim", "Hot Shave"],
  "hours": "Mon-Fri: 9am-7pm\\nSat: 10am-5pm\\nSun: Closed",
  "featured": true,
  "image_url": "https://example.com/shop-image.jpg", 
  "logo_url": "https://example.com/shop-logo.png",
  "neighborhood": "Downtown",
  "price_level": "$$",
  "category": "Barber shop"
}

Extract only valid barbershop information. If required fields are missing, set them to null. Output valid JSON only."""

        user_prompt = f"Extract barbershop information from this text and format as JSON: {raw_data}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        for attempt in range(retry_count):
            try:
                response = self.client.chat.completions.create(
                    model="deepseek-chat",
                    messages=messages,
                    response_format={'type': 'json_object'},
                    max_tokens=1000,
                    temperature=0.1
                )
                
                content = response.choices[0].message.content
                if content and content.strip():
                    return json.loads(content)
                else:
                    logger.warning(f"Empty response from DeepSeek API, attempt {attempt + 1}")
                    time.sleep(1)
                    
            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error: {e}, attempt {attempt + 1}")
                time.sleep(1)
            except Exception as e:
                logger.error(f"API error: {e}, attempt {attempt + 1}")
                time.sleep(2)
                
        return None
    
    def validate_barbershop_data(self, data: Dict) -> bool:
        """Validate that required fields are present"""
        required_fields = BARBERSHOP_SCHEMA['required']
        return all(data.get(field) for field in required_fields)
    
    def scrape_barbershops(self, city: str, state: str, max_results: int = 50) -> List[Dict]:
        """Main scraping function that combines multiple sources"""
        all_barbershops = []
        
        logger.info(f"Starting barbershop scraping for {city}, {state}")
        
        # Scrape from Yelp
        logger.info("Scraping Yelp...")
        yelp_data = self.scrape_yelp_barbershops(city, state, max_results // 2)
        
        # Scrape from Google Maps
        logger.info("Scraping Google Maps...")
        maps_data = self.scrape_google_maps_data("barbershop", f"{city}, {state}")
        
        # Combine all raw data
        all_raw_data = yelp_data + maps_data
        logger.info(f"Found {len(all_raw_data)} raw entries")
        
        # Process each entry with DeepSeek API
        for i, raw_text in enumerate(all_raw_data[:max_results]):
            logger.info(f"Processing entry {i+1}/{min(len(all_raw_data), max_results)}")
            
            processed_data = self.process_with_deepseek(raw_text)
            
            if processed_data and self.validate_barbershop_data(processed_data):
                all_barbershops.append(processed_data)
                logger.info(f"Successfully processed: {processed_data.get('name', 'Unknown')}")
            else:
                logger.warning(f"Failed to process or validate entry {i+1}")
            
            # Rate limiting between API calls
            time.sleep(random.uniform(0.5, 1.5))
            
        return all_barbershops
    
    def save_to_json(self, data: List[Dict], filename: str):
        """Save barbershop data to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved {len(data)} barbershops to {filename}")
        except Exception as e:
            logger.error(f"Error saving to file: {e}")

def main():
    # Configuration
    CITY = "San Diego"
    STATE = "CA"
    MAX_RESULTS = 30
    OUTPUT_FILE = "barbershops.json"

    # Initialize scraper (will use API key from config)
    scraper = BarbershopScraper()
    
    # Scrape barbershops
    barbershops = scraper.scrape_barbershops(CITY, STATE, MAX_RESULTS)
    
    # Save results
    scraper.save_to_json(barbershops, OUTPUT_FILE)
    
    print(f"\nScraping completed! Found {len(barbershops)} valid barbershops.")
    print(f"Results saved to {OUTPUT_FILE}")
    
    # Display sample results
    if barbershops:
        print("\nSample result:")
        print(json.dumps(barbershops[0], indent=2))

if __name__ == "__main__":
    main()