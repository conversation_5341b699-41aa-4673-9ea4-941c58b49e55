{% extends "base.html" %}

{% block title %}Barbershop Scraper - Find Local Barbershops{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Header -->
        <div class="text-center mb-5">
            <h1 class="display-4 text-primary">
                <i class="fas fa-cut me-3"></i>
                Barbershop Scraper
            </h1>
            <p class="lead text-muted">
                Discover local barbershops with detailed information powered by AI
            </p>
        </div>

        <!-- Search Form -->
        <div class="card shadow-lg">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">
                    <i class="fas fa-search me-2"></i>
                    Search Barbershops
                </h3>
            </div>
            <div class="card-body">
                <form action="{{ url_for('start_scraping') }}" method="POST" id="searchForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">
                                <i class="fas fa-city me-1"></i>City
                            </label>
                            <input type="text" class="form-control" id="city" name="city" 
                                   placeholder="Enter city name" required>
                            <div class="form-text">e.g., San Diego, Los Angeles, New York</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="state" class="form-label">
                                <i class="fas fa-map-marker-alt me-1"></i>State
                            </label>
                            <input type="text" class="form-control" id="state" name="state" 
                                   placeholder="Enter state code" required maxlength="2">
                            <div class="form-text">e.g., CA, NY, TX (2-letter code)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="max_results" class="form-label">
                                <i class="fas fa-list-ol me-1"></i>Maximum Results
                            </label>
                            <select class="form-select" id="max_results" name="max_results">
                                <option value="10">10 results</option>
                                <option value="20">20 results</option>
                                <option value="30" selected>30 results</option>
                                <option value="50">50 results</option>
                                <option value="100">100 results</option>
                            </select>
                            <div class="form-text">More results take longer to process</div>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-search me-2"></i>
                            Start Scraping
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Location Buttons -->
        <div class="mt-4">
            <h4 class="text-center mb-3">Quick Search</h4>
            <div class="row g-2">
                {% for location in locations %}
                <div class="col-md-4 col-sm-6">
                    <button class="btn btn-outline-primary w-100 quick-location" 
                            data-city="{{ location.city }}" 
                            data-state="{{ location.state }}">
                        <i class="fas fa-map-pin me-1"></i>
                        {{ location.city }}, {{ location.state }}
                    </button>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- Features -->
        <div class="row mt-5">
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon">
                    <i class="fas fa-robot fa-3x text-primary mb-3"></i>
                </div>
                <h5>AI-Powered</h5>
                <p class="text-muted">Uses DeepSeek AI to extract and structure barbershop data</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon">
                    <i class="fas fa-globe fa-3x text-primary mb-3"></i>
                </div>
                <h5>Multiple Sources</h5>
                <p class="text-muted">Scrapes from Yelp and Google Maps for comprehensive results</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="feature-icon">
                    <i class="fas fa-download fa-3x text-primary mb-3"></i>
                </div>
                <h5>Export Data</h5>
                <p class="text-muted">Download results as JSON for further analysis</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quick location buttons
    document.querySelectorAll('.quick-location').forEach(button => {
        button.addEventListener('click', function() {
            document.getElementById('city').value = this.dataset.city;
            document.getElementById('state').value = this.dataset.state;
        });
    });

    // Form validation
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        const city = document.getElementById('city').value.trim();
        const state = document.getElementById('state').value.trim();
        
        if (!city || !state) {
            e.preventDefault();
            alert('Please enter both city and state');
            return;
        }
        
        if (state.length !== 2) {
            e.preventDefault();
            alert('Please enter a valid 2-letter state code');
            return;
        }
    });
});
</script>
{% endblock %}
