import os
from typing import Dict, List

# API Configuration
DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY', 'your-api-key-here')
DEEPSEEK_BASE_URL = "https://api.deepseek.com"

# Scraping Configuration
SCRAPING_CONFIG = {
    'max_results_per_source': 25,
    'rate_limit_min': 0.5,
    'rate_limit_max': 2.0,
    'retry_attempts': 3,
    'request_timeout': 30
}

# Target locations for scraping
TARGET_LOCATIONS = [
    {"city": "San Diego", "state": "CA"},
    {"city": "Los Angeles", "state": "CA"},
    {"city": "San Francisco", "state": "CA"},
    {"city": "New York", "state": "NY"},
    {"city": "Chicago", "state": "IL"}
]

# DeepSeek API Configuration
DEEPSEEK_CONFIG = {
    'model': 'deepseek-chat',
    'max_tokens': 1000,
    'temperature': 0.1,
    'response_format': {'type': 'json_object'}
}

# Required and optional fields for barbershop data
BARBERSHOP_SCHEMA = {
    'required': ['name', 'address', 'city', 'phone'],
    'optional': [
        'state', 'zip', 'website', 'rating', 'review_count', 
        'latitude', 'longitude', 'description', 'services', 
        'hours', 'featured', 'image_url', 'logo_url', 
        'neighborhood', 'price_level', 'category'
    ]
}