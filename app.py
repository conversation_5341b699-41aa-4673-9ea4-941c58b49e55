from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from flask_cors import CORS
import json
import os
import uuid
import threading
import time
from datetime import datetime
from barbershop_scraper import BarbershopScraper
from config import TARGET_LOCATIONS

app = Flask(__name__)
CORS(app)

# Store scraping jobs in memory (in production, use Redis or database)
scraping_jobs = {}

class ScrapingJob:
    def __init__(self, job_id, city, state, max_results):
        self.job_id = job_id
        self.city = city
        self.state = state
        self.max_results = max_results
        self.status = 'pending'  # pending, running, completed, error
        self.progress = 0
        self.results = []
        self.error_message = None
        self.created_at = datetime.now()
        self.completed_at = None

def run_scraping_job(job_id):
    """Run scraping job in background thread"""
    job = scraping_jobs[job_id]
    try:
        job.status = 'running'
        job.progress = 10
        
        # Initialize scraper
        scraper = BarbershopScraper()
        job.progress = 20
        
        # Run scraping
        results = scraper.scrape_barbershops(job.city, job.state, job.max_results)
        job.progress = 90
        
        # Store results
        job.results = results
        job.status = 'completed'
        job.progress = 100
        job.completed_at = datetime.now()
        
    except Exception as e:
        job.status = 'error'
        job.error_message = str(e)
        job.progress = 0

@app.route('/')
def index():
    """Main page with search form"""
    return render_template('index.html', locations=TARGET_LOCATIONS)

@app.route('/scrape', methods=['POST'])
def start_scraping():
    """Start a new scraping job"""
    try:
        city = request.form.get('city', '').strip()
        state = request.form.get('state', '').strip()
        max_results = int(request.form.get('max_results', 30))
        
        if not city or not state:
            return jsonify({'error': 'City and state are required'}), 400
        
        if max_results < 1 or max_results > 100:
            return jsonify({'error': 'Max results must be between 1 and 100'}), 400
        
        # Create new job
        job_id = str(uuid.uuid4())
        job = ScrapingJob(job_id, city, state, max_results)
        scraping_jobs[job_id] = job
        
        # Start scraping in background thread
        thread = threading.Thread(target=run_scraping_job, args=(job_id,))
        thread.daemon = True
        thread.start()
        
        return redirect(url_for('progress', job_id=job_id))
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/progress/<job_id>')
def progress(job_id):
    """Show progress page"""
    if job_id not in scraping_jobs:
        return render_template('error.html', error='Job not found'), 404
    
    job = scraping_jobs[job_id]
    return render_template('progress.html', job=job)

@app.route('/api/status/<job_id>')
def get_job_status(job_id):
    """API endpoint to get job status"""
    if job_id not in scraping_jobs:
        return jsonify({'error': 'Job not found'}), 404
    
    job = scraping_jobs[job_id]
    return jsonify({
        'job_id': job.job_id,
        'status': job.status,
        'progress': job.progress,
        'city': job.city,
        'state': job.state,
        'results_count': len(job.results),
        'error_message': job.error_message,
        'created_at': job.created_at.isoformat(),
        'completed_at': job.completed_at.isoformat() if job.completed_at else None
    })

@app.route('/results/<job_id>')
def results(job_id):
    """Show results page"""
    if job_id not in scraping_jobs:
        return render_template('error.html', error='Job not found'), 404
    
    job = scraping_jobs[job_id]
    if job.status != 'completed':
        return redirect(url_for('progress', job_id=job_id))
    
    return render_template('results.html', job=job)

@app.route('/download/<job_id>')
def download_results(job_id):
    """Download results as JSON file"""
    if job_id not in scraping_jobs:
        return jsonify({'error': 'Job not found'}), 404
    
    job = scraping_jobs[job_id]
    if job.status != 'completed':
        return jsonify({'error': 'Job not completed'}), 400
    
    # Create temporary file
    filename = f"barbershops_{job.city}_{job.state}_{job.job_id[:8]}.json"
    filepath = os.path.join('temp', filename)
    
    # Ensure temp directory exists
    os.makedirs('temp', exist_ok=True)
    
    # Save results to file
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(job.results, f, indent=2, ensure_ascii=False)
    
    return send_file(filepath, as_attachment=True, download_name=filename)

@app.route('/api/locations')
def get_locations():
    """API endpoint to get predefined locations"""
    return jsonify(TARGET_LOCATIONS)

if __name__ == '__main__':
    # Clean up old temp files on startup
    if os.path.exists('temp'):
        for file in os.listdir('temp'):
            try:
                os.remove(os.path.join('temp', file))
            except:
                pass
    
    app.run(debug=True, host='0.0.0.0', port=5000)
