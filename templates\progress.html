{% extends "base.html" %}

{% block title %}Scraping Progress - Barbershop Scraper{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Header -->
        <div class="text-center mb-4">
            <h2 class="text-primary">
                <i class="fas fa-cog fa-spin me-2"></i>
                Scraping in Progress
            </h2>
            <p class="text-muted">
                Searching for barbershops in {{ job.city }}, {{ job.state }}
            </p>
        </div>

        <!-- Progress Card -->
        <div class="card shadow">
            <div class="card-body">
                <!-- Progress Bar -->
                <div class="mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-bold">Progress</span>
                        <span id="progress-text">{{ job.progress }}%</span>
                    </div>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" 
                             id="progress-bar"
                             style="width: {{ job.progress }}%"
                             aria-valuenow="{{ job.progress }}" 
                             aria-valuemin="0" 
                             aria-valuemax="100">
                        </div>
                    </div>
                </div>

                <!-- Status Information -->
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">Search Details</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-city me-2"></i><strong>City:</strong> {{ job.city }}</li>
                            <li><i class="fas fa-map-marker-alt me-2"></i><strong>State:</strong> {{ job.state }}</li>
                            <li><i class="fas fa-list-ol me-2"></i><strong>Max Results:</strong> {{ job.max_results }}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">Status</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-clock me-2"></i><strong>Started:</strong> {{ job.created_at.strftime('%H:%M:%S') }}</li>
                            <li><i class="fas fa-info-circle me-2"></i><strong>Status:</strong> 
                                <span id="status-badge" class="badge bg-primary">{{ job.status.title() }}</span>
                            </li>
                            <li><i class="fas fa-list me-2"></i><strong>Results Found:</strong> 
                                <span id="results-count">{{ job.results|length }}</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Current Step -->
                <div class="mt-4">
                    <h6 class="text-muted">Current Step</h6>
                    <div id="current-step" class="alert alert-info">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        <span id="step-text">Initializing scraper...</span>
                    </div>
                </div>

                <!-- Error Message (if any) -->
                <div id="error-container" class="mt-4" style="display: none;">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Error:</strong> <span id="error-message"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-4">
            <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Back to Search
            </a>
            <button id="refresh-btn" class="btn btn-outline-primary ms-2" onclick="location.reload()">
                <i class="fas fa-sync-alt me-2"></i>
                Refresh
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
const jobId = '{{ job.job_id }}';
let pollInterval;

function updateProgress(data) {
    // Update progress bar
    document.getElementById('progress-bar').style.width = data.progress + '%';
    document.getElementById('progress-bar').setAttribute('aria-valuenow', data.progress);
    document.getElementById('progress-text').textContent = data.progress + '%';
    
    // Update status badge
    const statusBadge = document.getElementById('status-badge');
    statusBadge.textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
    
    // Update badge color based on status
    statusBadge.className = 'badge ';
    switch(data.status) {
        case 'pending':
            statusBadge.className += 'bg-secondary';
            break;
        case 'running':
            statusBadge.className += 'bg-primary';
            break;
        case 'completed':
            statusBadge.className += 'bg-success';
            break;
        case 'error':
            statusBadge.className += 'bg-danger';
            break;
    }
    
    // Update results count
    document.getElementById('results-count').textContent = data.results_count;
    
    // Update current step
    const stepText = document.getElementById('step-text');
    const currentStep = document.getElementById('current-step');
    
    if (data.status === 'pending') {
        stepText.textContent = 'Waiting to start...';
        currentStep.className = 'alert alert-secondary';
    } else if (data.status === 'running') {
        if (data.progress <= 20) {
            stepText.textContent = 'Initializing scraper...';
        } else if (data.progress <= 50) {
            stepText.textContent = 'Scraping Yelp for barbershop listings...';
        } else if (data.progress <= 80) {
            stepText.textContent = 'Scraping Google Maps for additional data...';
        } else {
            stepText.textContent = 'Processing results with AI...';
        }
        currentStep.className = 'alert alert-info';
    } else if (data.status === 'completed') {
        stepText.innerHTML = '<i class="fas fa-check me-2"></i>Scraping completed successfully!';
        currentStep.className = 'alert alert-success';
        
        // Redirect to results page after a short delay
        setTimeout(() => {
            window.location.href = `/results/${jobId}`;
        }, 2000);
    } else if (data.status === 'error') {
        stepText.innerHTML = '<i class="fas fa-times me-2"></i>Scraping failed';
        currentStep.className = 'alert alert-danger';
        
        // Show error message
        document.getElementById('error-message').textContent = data.error_message || 'Unknown error occurred';
        document.getElementById('error-container').style.display = 'block';
    }
}

function pollStatus() {
    fetch(`/api/status/${jobId}`)
        .then(response => response.json())
        .then(data => {
            updateProgress(data);
            
            // Stop polling if job is completed or failed
            if (data.status === 'completed' || data.status === 'error') {
                clearInterval(pollInterval);
            }
        })
        .catch(error => {
            console.error('Error polling status:', error);
        });
}

// Start polling when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Initial update
    pollStatus();
    
    // Poll every 2 seconds
    pollInterval = setInterval(pollStatus, 2000);
});

// Clean up interval when page unloads
window.addEventListener('beforeunload', function() {
    if (pollInterval) {
        clearInterval(pollInterval);
    }
});
</script>
{% endblock %}
