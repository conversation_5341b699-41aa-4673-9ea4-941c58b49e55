{% extends "base.html" %}

{% block title %}Results - Barbershop Scraper{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="text-primary mb-1">
                    <i class="fas fa-cut me-2"></i>
                    Barbershop Results
                </h2>
                <p class="text-muted mb-0">
                    Found {{ job.results|length }} barbershops in {{ job.city }}, {{ job.state }}
                </p>
            </div>
            <div>
                <a href="{{ url_for('download_results', job_id=job.job_id) }}" 
                   class="btn btn-success me-2">
                    <i class="fas fa-download me-2"></i>
                    Download JSON
                </a>
                <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                    <i class="fas fa-search me-2"></i>
                    New Search
                </a>
            </div>
        </div>

        <!-- Summary Stats -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-store fa-2x mb-2"></i>
                        <h4>{{ job.results|length }}</h4>
                        <small>Total Found</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-star fa-2x mb-2"></i>
                        <h4>{{ job.results|selectattr('rating')|list|length }}</h4>
                        <small>With Ratings</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-phone fa-2x mb-2"></i>
                        <h4>{{ job.results|selectattr('phone')|list|length }}</h4>
                        <small>With Phone</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <i class="fas fa-globe fa-2x mb-2"></i>
                        <h4>{{ job.results|selectattr('website')|list|length }}</h4>
                        <small>With Website</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="sort-select" class="form-label">Sort by:</label>
                        <select id="sort-select" class="form-select">
                            <option value="name">Name (A-Z)</option>
                            <option value="rating">Rating (High to Low)</option>
                            <option value="review_count">Review Count</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="filter-rating" class="form-label">Minimum Rating:</label>
                        <select id="filter-rating" class="form-select">
                            <option value="0">All Ratings</option>
                            <option value="3">3+ Stars</option>
                            <option value="4">4+ Stars</option>
                            <option value="4.5">4.5+ Stars</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="search-filter" class="form-label">Search:</label>
                        <input type="text" id="search-filter" class="form-control" 
                               placeholder="Search by name or address...">
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Grid -->
        <div id="results-container" class="row">
            {% for barbershop in job.results %}
            <div class="col-lg-6 col-xl-4 mb-4 barbershop-card" 
                 data-name="{{ barbershop.name|lower }}"
                 data-address="{{ barbershop.address|lower }}"
                 data-rating="{{ barbershop.rating or 0 }}">
                <div class="card h-100 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title text-primary mb-0">{{ barbershop.name }}</h5>
                            {% if barbershop.rating %}
                            <div class="text-end">
                                <div class="text-warning">
                                    {% for i in range(5) %}
                                        {% if i < barbershop.rating %}
                                            <i class="fas fa-star"></i>
                                        {% else %}
                                            <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <small class="text-muted">{{ barbershop.rating }}/5</small>
                                {% if barbershop.review_count %}
                                <small class="text-muted">({{ barbershop.review_count }} reviews)</small>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <p class="card-text mb-1">
                                <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                {{ barbershop.address }}
                            </p>
                            {% if barbershop.city %}
                            <p class="card-text mb-1 text-muted">
                                {{ barbershop.city }}{% if barbershop.state %}, {{ barbershop.state }}{% endif %}
                                {% if barbershop.zip %} {{ barbershop.zip }}{% endif %}
                            </p>
                            {% endif %}
                        </div>

                        {% if barbershop.description %}
                        <p class="card-text text-muted small mb-3">{{ barbershop.description[:100] }}{% if barbershop.description|length > 100 %}...{% endif %}</p>
                        {% endif %}

                        {% if barbershop.services %}
                        <div class="mb-3">
                            <small class="text-muted d-block mb-1">Services:</small>
                            {% for service in barbershop.services[:3] %}
                            <span class="badge bg-light text-dark me-1">{{ service }}</span>
                            {% endfor %}
                            {% if barbershop.services|length > 3 %}
                            <span class="badge bg-secondary">+{{ barbershop.services|length - 3 }} more</span>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <div class="row g-2">
                            {% if barbershop.phone %}
                            <div class="col-6">
                                <a href="tel:{{ barbershop.phone }}" class="btn btn-outline-primary btn-sm w-100">
                                    <i class="fas fa-phone me-1"></i>Call
                                </a>
                            </div>
                            {% endif %}
                            {% if barbershop.website %}
                            <div class="col-6">
                                <a href="{{ barbershop.website }}" target="_blank" class="btn btn-outline-success btn-sm w-100">
                                    <i class="fas fa-globe me-1"></i>Website
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        {% if not job.results %}
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No barbershops found</h4>
            <p class="text-muted">Try searching in a different location or with different parameters.</p>
            <a href="{{ url_for('index') }}" class="btn btn-primary">
                <i class="fas fa-search me-2"></i>
                Try Another Search
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const sortSelect = document.getElementById('sort-select');
    const ratingFilter = document.getElementById('filter-rating');
    const searchFilter = document.getElementById('search-filter');
    const resultsContainer = document.getElementById('results-container');
    
    function filterAndSort() {
        const cards = Array.from(document.querySelectorAll('.barbershop-card'));
        const sortBy = sortSelect.value;
        const minRating = parseFloat(ratingFilter.value);
        const searchTerm = searchFilter.value.toLowerCase();
        
        // Filter cards
        cards.forEach(card => {
            const rating = parseFloat(card.dataset.rating);
            const name = card.dataset.name;
            const address = card.dataset.address;
            
            const matchesRating = rating >= minRating;
            const matchesSearch = !searchTerm || 
                                name.includes(searchTerm) || 
                                address.includes(searchTerm);
            
            card.style.display = matchesRating && matchesSearch ? 'block' : 'none';
        });
        
        // Sort visible cards
        const visibleCards = cards.filter(card => card.style.display !== 'none');
        
        visibleCards.sort((a, b) => {
            if (sortBy === 'name') {
                return a.dataset.name.localeCompare(b.dataset.name);
            } else if (sortBy === 'rating') {
                return parseFloat(b.dataset.rating) - parseFloat(a.dataset.rating);
            }
            return 0;
        });
        
        // Reorder in DOM
        visibleCards.forEach(card => {
            resultsContainer.appendChild(card);
        });
    }
    
    // Add event listeners
    sortSelect.addEventListener('change', filterAndSort);
    ratingFilter.addEventListener('change', filterAndSort);
    searchFilter.addEventListener('input', filterAndSort);
});
</script>
{% endblock %}
