I need you to extract information about barber shops and format it as a JSON object. For each barbershop, please include the following fields. If a field is not available, you can omit it or set its value to null, unless it's explicitly marked as required.

Required fields: name, address, city, phone.

Optional fields: state, zip, website, rating, review_count, latitude, longitude, description, services (as an array of strings), hours (formatted as multiline text for each day if possible), featured (boolean), image_url, logo_url, neighborhood, price_level, category.

Here's an example of the desired JSON structure:

{
  "name": "Example Barbershop",
  "address": "123 Main St",
  "city": "San Diego",
  "state": "CA",
  "zip": "92101",
  "phone": "(*************",
  "website": "http://www.examplebarbershop.com",
  "rating": 4.7,
  "review_count": 150,
  "latitude": 32.7157,
  "longitude": -117.1611,
  "description": "A brief description of the barbershop.",
  "services": ["Haircuts", "Beard Trim", "Hot Shave"],
  "hours": "Mon-Fri: 9am-7pm\nSat: 10am-5pm\nSun: Closed",
  "featured": true,
  "image_url": "https://example.com/shop-image.jpg",
  "logo_url": "https://example.com/shop-logo.png",
  "neighborhood": "Downtown",
  "price_level": "$$",
  "category": "Barber shop"
}
Please provide the JSON output for 
[
1. **Cutz By Percy**
   *Category:* Barber shop
   *Address:* 4240 Kearny Mesa Rd 105 Studio 2
   *City:* San Diego
   *ZIP:* 92111
   *State:* California

2. **Mellow Blendz Barber Studio**
   *Category:* Barber shop
   *Address:* 7610 Hazard Center Dr Suite 703-405
   *City:* San Diego
   *ZIP:* 92108
   *State:* California
].