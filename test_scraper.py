#!/usr/bin/env python3
"""
Test script to debug the barbershop scraper
"""

import requests
from bs4 import BeautifulSoup
from urllib.parse import quote
import json
from barbershop_scraper import BarbershopS<PERSON>raper

def test_yelp_access():
    """Test if we can access Yelp"""
    print("Testing Yelp access...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    city = "San Diego"
    state = "CA"
    search_url = f"https://www.yelp.com/search?find_desc=barbershop&find_loc={quote(city)}%2C+{quote(state)}"
    
    try:
        response = session.get(search_url)
        print(f"Yelp Status Code: {response.status_code}")
        print(f"Response Length: {len(response.content)}")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Try different selectors
        selectors = [
            'div[data-testid="serp-ia-card"]',
            'div[class*="businessName"]',
            'div[class*="container"]',
            'a[class*="businessName"]'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            print(f"Selector '{selector}': Found {len(elements)} elements")
            
        # Print first 1000 characters of HTML to see structure
        print("\nFirst 1000 characters of HTML:")
        print(response.text[:1000])
        
    except Exception as e:
        print(f"Error accessing Yelp: {e}")

def test_google_maps_access():
    """Test if we can access Google Maps"""
    print("\nTesting Google Maps access...")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    query = "barbershop"
    location = "San Diego, CA"
    search_url = f"https://www.google.com/maps/search/{quote(query)}+{quote(location)}"
    
    try:
        response = session.get(search_url)
        print(f"Google Maps Status Code: {response.status_code}")
        print(f"Response Length: {len(response.content)}")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Try different selectors
        selectors = [
            'div[class*="fontBodyMedium"]',
            'div[class*="fontBodySmall"]',
            'div[class*="place"]',
            'div[data-value]'
        ]
        
        for selector in selectors:
            elements = soup.select(selector)
            print(f"Selector '{selector}': Found {len(elements)} elements")
            
        # Print first 1000 characters of HTML to see structure
        print("\nFirst 1000 characters of HTML:")
        print(response.text[:1000])
        
    except Exception as e:
        print(f"Error accessing Google Maps: {e}")

def test_deepseek_api():
    """Test DeepSeek API with sample data"""
    print("\nTesting DeepSeek API...")
    
    try:
        scraper = BarbershopScraper()
        
        # Test with sample barbershop data
        sample_data = """
        Cutz By Percy
        Category: Barber shop
        Address: 4240 Kearny Mesa Rd 105 Studio 2
        City: San Diego
        ZIP: 92111
        State: California
        """
        
        result = scraper.process_with_deepseek(sample_data)
        
        if result:
            print("DeepSeek API working! Sample result:")
            print(json.dumps(result, indent=2))
        else:
            print("DeepSeek API failed to process sample data")
            
    except Exception as e:
        print(f"Error testing DeepSeek API: {e}")

def process_existing_data():
    """Process the existing barbershop data from babershops.md"""
    print("\nProcessing existing barbershop data...")
    
    try:
        # Read the existing data
        with open('babershops.md', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract barbershop entries (simplified parsing)
        barbershops_text = []
        lines = content.split('\n')
        current_shop = ""
        
        for line in lines:
            line = line.strip()
            if line.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '10.')):
                if current_shop:
                    barbershops_text.append(current_shop)
                current_shop = line
            elif line.startswith('*') and current_shop:
                current_shop += "\n" + line
        
        if current_shop:
            barbershops_text.append(current_shop)
        
        print(f"Found {len(barbershops_text)} barbershops in babershops.md")
        
        # Process with DeepSeek API
        scraper = BarbershopScraper()
        processed_barbershops = []
        
        for i, shop_text in enumerate(barbershops_text):
            print(f"Processing barbershop {i+1}/{len(barbershops_text)}...")
            result = scraper.process_with_deepseek(shop_text)
            
            if result and scraper.validate_barbershop_data(result):
                processed_barbershops.append(result)
                print(f"✓ Successfully processed: {result.get('name', 'Unknown')}")
            else:
                print(f"✗ Failed to process barbershop {i+1}")
        
        # Save results
        if processed_barbershops:
            output_file = "barbershops_from_md.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(processed_barbershops, f, indent=2, ensure_ascii=False)
            
            print(f"\n✅ Successfully processed {len(processed_barbershops)} barbershops!")
            print(f"Results saved to {output_file}")
            
            # Show sample result
            if processed_barbershops:
                print("\nSample result:")
                print(json.dumps(processed_barbershops[0], indent=2))
        else:
            print("❌ No barbershops were successfully processed")
            
    except Exception as e:
        print(f"Error processing existing data: {e}")

if __name__ == "__main__":
    print("🔧 Barbershop Scraper Debug Tool")
    print("=" * 50)
    
    # Run tests
    test_yelp_access()
    test_google_maps_access()
    test_deepseek_api()
    process_existing_data()
    
    print("\n" + "=" * 50)
    print("Debug complete!")
